name: Go CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}
    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v6
      with:
        version: latest
        args: --timeout=5m

  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Start MongoDB (Replica Set)
      run: |
        docker run --rm -d --name mongo \
          -p 27017:27017 \
          mongo:latest \
          --replSet rs0 --bind_ip_all
      # reference:  https://engineering.synatic.com/a-simple-way-to-run-a-mongodb-replica-set-in-github-actions

    - name: Wait for MongoDB to start
      run: |
        echo "Waiting for MongoDB to become available..."
        for i in {1..20}; do
          nc -z localhost 27017 && echo "MongoDB is up!" && break
          echo "Waiting..."
          sleep 1
        done

    - name: Initialize Mongo Replica Set
      run: |
        docker run --rm --network host mongo:latest \
          mongosh --host localhost --eval 'rs.initiate({_id: "rs0", members: [{_id: 0, host: "localhost:27017"}]})'

    - name: Test MongoDB
      run: |
        docker run --rm --network host mongo:latest \
          mongosh --host localhost --eval 'db.testDocs.insertOne({ok: true})'

    - name: Tidy up Go modules
      run: go mod tidy

    - name: Build
      run: go build -v ./...

    - name: revise local.test.ini for test
      run: |
        sed -i 's|mongodb://[^"]*|mongodb://localhost:27017/test|g' local.test.ini
        cat local.test.ini

    - name: Run Tests and Generate Coverage
      run: |
        pwd
        ls
        cat local.test.ini
        go test -coverprofile=coverage.out ./...
        cat coverage.out

    - name: Install bc
      run: sudo apt-get update && sudo apt-get install -y bc

    - name: Check Coverage Threshold (>= 80%)
      run: |
        coverage=$(go tool cover -func=coverage.out | grep total: | awk '{print substr($3, 1, length($3)-1)}')
        echo "Total coverage: $coverage%"
        threshold=80.0
        above=$(echo "$coverage >= $threshold" | bc -l)
        if [ "$above" -ne 1 ]; then
          echo "❌ Code coverage ($coverage%) is below threshold ($threshold%)"
          exit 1
        else
          echo "✅ Code coverage ($coverage%) meets threshold ($threshold%)"
        fi

    - name: Stop MongoDB
      run: |
        docker rm -f mongo

  test-with-s3:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Start MongoDB (Replica Set)
      run: |
        docker run --rm -d --name mongo \
          -p 27017:27017 \
          mongo:latest \
          --replSet rs0 --bind_ip_all

    - name: Wait for MongoDB to start
      run: |
        echo "Waiting for MongoDB to become available..."
        for i in {1..20}; do
          nc -z localhost 27017 && echo "MongoDB is up!" && break
          echo "Waiting..."
          sleep 1
        done

    - name: Initialize Mongo Replica Set
      run: |
        docker run --rm --network host mongo:latest \
          mongosh --host localhost --eval 'rs.initiate({_id: "rs0", members: [{_id: 0, host: "localhost:27017"}]})'

    - name: Start Garage S3 Service
      run: ./start-garage-ci.sh

    - name: Tidy up Go modules
      run: go mod tidy

    - name: Build
      run: go build -v ./...

    - name: Prepare test configuration for CI
      run: |
        # 只修改 MongoDB 连接字符串，S3 密钥由 start-garage-ci.sh 动态生成
        sed -i 's|mongodb://[^"]*|mongodb://localhost:27017/test|g' local.test.ini
        echo "=== MongoDB 配置已更新 ==="
        grep "uri =" local.test.ini

    - name: Run S3 Integration Tests
      run: |
        echo "=== 运行 S3 集成测试 ==="
        # 运行所有 S3 相关测试
        go test -v -run "TestDelete.*S3|TestWriteFileToS3_RealS3|TestDeleteS3Object_RealS3|TestCompleteChunkedUpload_MultiStorage" ./...

    - name: Run Full Test Suite with Coverage
      run: |
        echo "=== 运行完整测试套件 ==="
        go test -coverprofile=coverage.out ./...

    - name: Install bc
      run: sudo apt-get update && sudo apt-get install -y bc

    - name: Check Coverage Threshold (>= 85%)
      run: |
        coverage=$(go tool cover -func=coverage.out | grep total: | awk '{print substr($3, 1, length($3)-1)}')
        echo "Total coverage: $coverage%"
        threshold=85.0
        above=$(echo "$coverage >= $threshold" | bc -l)
        if [ "$above" -ne 1 ]; then
          echo "❌ S3 集成测试覆盖率 ($coverage%) 低于阈值 ($threshold%)"
          exit 1
        else
          echo "✅ S3 集成测试覆盖率 ($coverage%) 达到阈值 ($threshold%)"
        fi

    - name: Generate Coverage Report
      run: |
        echo "=== 详细覆盖率报告 ==="
        go tool cover -func=coverage.out | grep -E "(Delete|delete|S3|s3)"
        echo "=== 整体覆盖率 ==="
        go tool cover -func=coverage.out | tail -1

    - name: Stop Services
      if: always()
      run: |
        echo "=== 清理服务 ==="
        ./stop-garage-ci.sh || true
        docker rm -f mongo || true
