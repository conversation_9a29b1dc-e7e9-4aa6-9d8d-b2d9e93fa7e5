# S3 测试环境设置指南

本指南帮助你设置本地 S3 兼容服务（Garage）来支持 goupload 项目的 S3 相关测试。

## 前置要求

你需要安装 Docker 或 Podman 其中之一：

### 安装 Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
# 重新登录或运行: newgrp docker

# 验证安装
docker --version
```

### 安装 Podman

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y podman

# CentOS/RHEL/Fedora
sudo dnf install -y podman

# 验证安装
podman --version
```

## 快速开始

### 1. 启动 Garage S3 服务

```bash
./start-garage.sh
```

这个脚本会：
- 创建 Garage 配置文件
- 启动 Garage 容器（端口 3900-3904）
- 配置集群和存储桶
- 创建测试用的访问密钥

### 2. 测试 S3 连接

```bash
./test-s3-connection.sh
```

### 3. 运行 S3 相关测试

```bash
# 运行所有 S3 相关的删除测试
go test -v -run "TestDelete.*S3" ./...

# 运行所有测试
go test -v ./...
```

### 4. 停止服务

```bash
./stop-garage.sh
```

## 服务信息

启动后，Garage 提供以下服务：

- **S3 API**: http://localhost:3900
- **Admin API**: http://localhost:3903  
- **Web UI**: http://localhost:3902
- **RPC**: http://localhost:3901

## 测试配置

配置文件 `local.test.ini` 中已经包含了以下 S3 配置：

### Primary 访问密钥
- Access Key: `GK4d1095e87554826d40f6af89`
- Secret Key: `02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3`

### Secondary 访问密钥  
- Access Key: `GK1e8266fe625fd9e3d392d497`
- Secret Key: `0130431df8aa44ddee23777cfdc369b8dab1f51777d4308b49655aa0bdd85bc1`

### 存储桶
- `test-bucket`
- `test-multi-bucket`

### 区域
- `garage`

## 测试类型配置

在 `local.test.ini` 中配置了以下测试类型：

1. **test_s3_only**: 仅使用 S3 存储
2. **test_multi_storage**: 同时使用本地和 S3 存储

## 使用 AWS CLI 测试（可选）

如果安装了 AWS CLI，可以手动测试：

```bash
# 安装 AWS CLI
pip install awscli

# 设置环境变量
export AWS_ACCESS_KEY_ID="GK4d1095e87554826d40f6af89"
export AWS_SECRET_ACCESS_KEY="02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
export AWS_DEFAULT_REGION="garage"

# 列出存储桶
aws --endpoint-url=http://localhost:3900 s3 ls

# 上传文件
echo "Hello S3!" > test.txt
aws --endpoint-url=http://localhost:3900 s3 cp test.txt s3://test-bucket/

# 下载文件
aws --endpoint-url=http://localhost:3900 s3 cp s3://test-bucket/test.txt downloaded.txt

# 删除文件
aws --endpoint-url=http://localhost:3900 s3 rm s3://test-bucket/test.txt
```

## 故障排除

### 容器启动失败

```bash
# 查看容器日志
docker logs garage-s3
# 或
podman logs garage-s3
```

### 端口冲突

如果端口 3900-3904 被占用，可以修改 `start-garage.sh` 中的端口映射。

### 权限问题

确保当前用户有权限运行 Docker/Podman：

```bash
# 对于 Docker
sudo usermod -aG docker $USER
newgrp docker

# 对于 Podman，通常不需要特殊权限
```

### 清理数据

如果需要完全重置：

```bash
./stop-garage.sh
rm -rf garage-data garage-meta garage.toml
./start-garage.sh
```

## 集成到 CI/CD

### GitHub Actions 集成

项目已经集成了 Garage S3 到 GitHub Actions 工作流中。当你推送代码到远程仓库时，会自动运行包含 S3 测试的完整测试套件。

#### 工作流配置

在 `.github/workflows/go.yml` 中有两个测试作业：

1. **test**: 基础测试（不包含 S3）
2. **test-with-s3**: S3 集成测试

#### 本地脚本 vs CI 脚本

- **本地开发**: 使用 `./start-garage.sh` 和 `./stop-garage.sh`
- **CI 环境**: 使用 `./start-garage-ci.sh` 和 `./stop-garage-ci.sh`

CI 脚本针对 GitHub Actions 环境进行了优化：
- 更严格的错误处理
- 更详细的日志输出
- 适合容器化环境的配置

#### 手动触发 CI

你可以通过以下方式触发 CI：

```bash
# 推送到 main 分支
git push origin main

# 创建 Pull Request 到 main 分支
git checkout -b feature/my-feature
git push origin feature/my-feature
# 然后在 GitHub 上创建 PR

# 手动触发工作流
# 在 GitHub 仓库页面 -> Actions -> Go CI -> Run workflow
```

#### CI 测试覆盖率要求

- **基础测试**: 覆盖率 ≥ 80%
- **S3 集成测试**: 覆盖率 ≥ 85%

#### 其他 CI/CD 平台

对于其他 CI/CD 平台，可以参考以下模板：

```yaml
# GitLab CI 示例
test-with-s3:
  image: golang:1.21
  services:
    - mongo:latest
  before_script:
    - ./start-garage-ci.sh
  script:
    - go test -v ./...
  after_script:
    - ./stop-garage-ci.sh

# Jenkins Pipeline 示例
pipeline {
  agent any
  stages {
    stage('Setup') {
      steps {
        sh './start-garage-ci.sh'
      }
    }
    stage('Test') {
      steps {
        sh 'go test -v ./...'
      }
    }
  }
  post {
    always {
      sh './stop-garage-ci.sh'
    }
  }
}
```
