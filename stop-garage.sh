#!/bin/bash

# Garage S3 兼容服务停止脚本

set -e

# 检查容器工具
CONTAINER_CMD=""
if command -v podman &> /dev/null; then
    CONTAINER_CMD="podman"
elif command -v docker &> /dev/null; then
    CONTAINER_CMD="docker"
else
    echo "❌ 错误: 未找到 Docker 或 Podman"
    exit 1
fi

echo "🛑 停止 Garage S3 服务..."

# 停止并删除容器
if $CONTAINER_CMD ps -a --format "{{.Names}}" | grep -q "^garage-s3$"; then
    echo "🔄 停止 Garage 容器..."
    $CONTAINER_CMD stop garage-s3

    echo "🗑️  删除 Garage 容器..."
    $CONTAINER_CMD rm garage-s3

    echo "✅ Garage 服务已停止"
else
    echo "ℹ️  没有找到运行中的 Garage 容器"
fi

# 可选：清理数据目录（取消注释以启用）
# echo "🧹 清理数据目录..."
# rm -rf ./garage-data ./garage-meta ./garage.toml

echo "🎉 清理完成！"
