#!/bin/bash

# 测试动态密钥生成和配置更新的脚本

set -e

echo "🧪 测试动态密钥生成和配置更新..."

# 备份原始配置文件
if [ -f "local.test.ini" ]; then
    cp local.test.ini local.test.ini.original
    echo "✅ 已备份原始配置文件"
else
    echo "❌ 错误: local.test.ini 文件不存在"
    exit 1
fi

# 创建新的测试密钥（使用时间戳确保唯一性）
TIMESTAMP=$(date +%s)
echo "🔑 创建测试密钥 (时间戳: $TIMESTAMP)..."
PRIMARY_OUTPUT=$(docker exec garage-s3 /garage key create test-dynamic-primary-$TIMESTAMP)
SECONDARY_OUTPUT=$(docker exec garage-s3 /garage key create test-dynamic-secondary-$TIMESTAMP)

echo "Primary 输出:"
echo "$PRIMARY_OUTPUT"
echo "---"
echo "Secondary 输出:"
echo "$SECONDARY_OUTPUT"
echo "---"

# 提取密钥信息
PRIMARY_KEY=$(echo "$PRIMARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
PRIMARY_SECRET=$(echo "$PRIMARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')
SECONDARY_KEY=$(echo "$SECONDARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
SECONDARY_SECRET=$(echo "$SECONDARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')

echo "📝 提取的密钥信息:"
echo "Primary - Key: $PRIMARY_KEY"
echo "Primary - Secret: $PRIMARY_SECRET"
echo "Secondary - Key: $SECONDARY_KEY"  
echo "Secondary - Secret: $SECONDARY_SECRET"

# 验证密钥不为空
if [ -z "$PRIMARY_KEY" ] || [ -z "$PRIMARY_SECRET" ] || [ -z "$SECONDARY_KEY" ] || [ -z "$SECONDARY_SECRET" ]; then
    echo "❌ 错误: 密钥提取失败"
    exit 1
fi

# 设置现有存储桶权限（测试需要的存储桶）
echo "🔐 设置存储桶权限..."
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-dynamic-primary-$TIMESTAMP
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-dynamic-secondary-$TIMESTAMP
docker exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-dynamic-primary-$TIMESTAMP
docker exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-dynamic-secondary-$TIMESTAMP

# 更新配置文件
echo "📝 更新配置文件..."
awk -v pk="$PRIMARY_KEY" -v ps="$PRIMARY_SECRET" -v sk="$SECONDARY_KEY" -v ss="$SECONDARY_SECRET" '
/^\[\[connection_sources\.s3_providers\]\]/ { provider_count++ }
/^key = / { 
    if (provider_count == 1) print "key = \"" pk "\""
    else if (provider_count == 2) print "key = \"" sk "\""
    else print
    next
}
/^pass = / { 
    if (provider_count == 1) print "pass = \"" ps "\""
    else if (provider_count == 2) print "pass = \"" ss "\""
    else print
    next
}
{ print }
' local.test.ini > local.test.ini.tmp && mv local.test.ini.tmp local.test.ini

echo "✅ 配置文件已更新"
echo "=== 更新后的 S3 配置 ==="
grep -A 6 "connection_sources.s3_providers" local.test.ini

# 测试配置是否正确
echo "🧪 测试更新后的配置..."
if go test -v -run "TestWriteFileToS3_RealS3_Success" ./... 2>/dev/null; then
    echo "✅ 动态密钥配置测试成功！"
else
    echo "❌ 动态密钥配置测试失败"
fi

# 恢复原始配置文件
echo "🔄 恢复原始配置文件..."
mv local.test.ini.original local.test.ini
echo "✅ 原始配置文件已恢复"

# 清理测试密钥
echo "🧹 清理测试密钥..."
docker exec garage-s3 /garage key delete --yes test-dynamic-primary-$TIMESTAMP 2>/dev/null || true
docker exec garage-s3 /garage key delete --yes test-dynamic-secondary-$TIMESTAMP 2>/dev/null || true

echo "🎉 动态密钥测试完成！"
