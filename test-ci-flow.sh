#!/bin/bash

# 测试完整的 CI 流程：启动服务 -> 动态生成密钥 -> 更新配置 -> 运行测试

set -e

echo "🚀 测试完整的 CI 流程..."

# 停止现有服务
echo "🛑 停止现有服务..."
./stop-garage-ci.sh 2>/dev/null || ./stop-garage.sh 2>/dev/null || true

# 备份原始配置文件
if [ -f "local.test.ini" ]; then
    cp local.test.ini local.test.ini.ci-backup
    echo "✅ 已备份原始配置文件"
else
    echo "❌ 错误: local.test.ini 文件不存在"
    exit 1
fi

# 启动 CI 服务并动态生成密钥
echo "🚀 启动 CI 服务..."
./start-garage-ci.sh

# 验证配置文件是否已更新
echo "🔍 验证配置文件更新..."
echo "=== 更新后的 S3 配置 ==="
grep -A 6 "connection_sources.s3_providers" local.test.ini | head -15

# 运行 S3 相关测试
echo "🧪 运行 S3 相关测试..."
if go test -v -run "TestWriteFileToS3_RealS3_Success|TestDeleteS3Object_RealS3_Success" ./...; then
    echo "✅ S3 测试成功！"
    TEST_RESULT="SUCCESS"
else
    echo "❌ S3 测试失败"
    TEST_RESULT="FAILED"
fi

# 运行完整测试套件
echo "🧪 运行完整测试套件..."
if go test -coverprofile=coverage.out ./...; then
    echo "✅ 完整测试套件成功！"
    FULL_TEST_RESULT="SUCCESS"
    
    # 显示覆盖率
    echo "📊 测试覆盖率:"
    go tool cover -func=coverage.out | tail -5
else
    echo "❌ 完整测试套件失败"
    FULL_TEST_RESULT="FAILED"
fi

# 停止服务
echo "🛑 停止 CI 服务..."
./stop-garage-ci.sh

# 恢复原始配置文件
echo "🔄 恢复原始配置文件..."
mv local.test.ini.ci-backup local.test.ini
echo "✅ 原始配置文件已恢复"

# 总结结果
echo ""
echo "🎯 CI 流程测试结果:"
echo "  - S3 测试: $TEST_RESULT"
echo "  - 完整测试: $FULL_TEST_RESULT"

if [ "$TEST_RESULT" = "SUCCESS" ] && [ "$FULL_TEST_RESULT" = "SUCCESS" ]; then
    echo "🎉 CI 流程测试完全成功！"
    echo ""
    echo "✅ 你现在可以安全地推送到远程仓库："
    echo "   git add ."
    echo "   git commit -m 'Add dynamic S3 key generation for CI'"
    echo "   git push origin main"
    echo ""
    echo "🚀 GitHub Actions 将会："
    echo "   1. 启动 MongoDB 副本集"
    echo "   2. 启动 Garage S3 服务"
    echo "   3. 动态生成访问密钥"
    echo "   4. 更新配置文件"
    echo "   5. 运行完整测试套件"
    echo "   6. 验证 85% 覆盖率要求"
    echo "   7. 清理所有服务"
    exit 0
else
    echo "❌ CI 流程测试失败，请检查问题后再推送"
    exit 1
fi
