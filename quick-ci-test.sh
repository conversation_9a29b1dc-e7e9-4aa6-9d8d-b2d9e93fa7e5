#!/bin/bash

# 快速测试 CI 关键功能：动态密钥生成和配置更新

set -e

echo "🚀 快速 CI 功能测试..."

# 检查当前服务状态
if docker ps -q -f name=garage-s3 | grep -q .; then
    echo "✅ Garage 服务正在运行"
else
    echo "❌ Garage 服务未运行，请先启动服务"
    exit 1
fi

# 检查服务是否响应
if curl -s -m 2 --connect-timeout 1 http://127.0.0.1:3900 >/dev/null 2>&1; then
    echo "✅ Garage S3 API 可访问"
else
    echo "❌ Garage S3 API 不可访问"
    exit 1
fi

# 备份配置文件
cp local.test.ini local.test.ini.quick-backup

# 测试动态密钥生成
TIMESTAMP=$(date +%s)
echo "🔑 测试动态密钥生成 (时间戳: $TIMESTAMP)..."

# 创建测试密钥
PRIMARY_OUTPUT=$(docker exec garage-s3 /garage key create test-quick-primary-$TIMESTAMP)
SECONDARY_OUTPUT=$(docker exec garage-s3 /garage key create test-quick-secondary-$TIMESTAMP)

# 提取密钥信息
PRIMARY_KEY=$(echo "$PRIMARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
PRIMARY_SECRET=$(echo "$PRIMARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')
SECONDARY_KEY=$(echo "$SECONDARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
SECONDARY_SECRET=$(echo "$SECONDARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')

echo "✅ 密钥生成成功:"
echo "  Primary: $PRIMARY_KEY"
echo "  Secondary: $SECONDARY_KEY"

# 设置存储桶权限
echo "🔐 设置存储桶权限..."
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-quick-primary-$TIMESTAMP
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-quick-secondary-$TIMESTAMP

# 更新配置文件
echo "📝 更新配置文件..."
awk -v pk="$PRIMARY_KEY" -v ps="$PRIMARY_SECRET" -v sk="$SECONDARY_KEY" -v ss="$SECONDARY_SECRET" '
/^\[\[connection_sources\.s3_providers\]\]/ { provider_count++ }
/^key = / { 
    if (provider_count == 1) print "key = \"" pk "\""
    else if (provider_count == 2) print "key = \"" sk "\""
    else print
    next
}
/^pass = / { 
    if (provider_count == 1) print "pass = \"" ps "\""
    else if (provider_count == 2) print "pass = \"" ss "\""
    else print
    next
}
{ print }
' local.test.ini > local.test.ini.tmp && mv local.test.ini.tmp local.test.ini

echo "✅ 配置文件已更新"

# 快速测试 S3 功能
echo "🧪 快速测试 S3 功能..."
if timeout 30 go test -v -run "TestWriteFileToS3_RealS3_Success" ./... 2>/dev/null; then
    echo "✅ S3 功能测试成功！"
    TEST_RESULT="SUCCESS"
else
    echo "❌ S3 功能测试失败"
    TEST_RESULT="FAILED"
fi

# 恢复配置文件
echo "🔄 恢复配置文件..."
mv local.test.ini.quick-backup local.test.ini

# 清理测试密钥
echo "🧹 清理测试密钥..."
docker exec garage-s3 /garage key delete --yes test-quick-primary-$TIMESTAMP 2>/dev/null || true
docker exec garage-s3 /garage key delete --yes test-quick-secondary-$TIMESTAMP 2>/dev/null || true

# 结果
echo ""
echo "🎯 快速测试结果: $TEST_RESULT"

if [ "$TEST_RESULT" = "SUCCESS" ]; then
    echo "🎉 CI 核心功能正常！"
    echo ""
    echo "✅ 动态密钥生成: 正常"
    echo "✅ 配置文件更新: 正常"
    echo "✅ S3 功能测试: 正常"
    echo ""
    echo "🚀 你可以安全地推送到远程仓库了！"
    exit 0
else
    echo "❌ CI 核心功能异常，请检查问题"
    exit 1
fi
