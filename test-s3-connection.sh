#!/bin/bash

# 测试 Garage S3 连接的脚本

set -e

# 检查容器工具
CONTAINER_CMD=""
if command -v podman &> /dev/null; then
    CONTAINER_CMD="podman"
elif command -v docker &> /dev/null; then
    CONTAINER_CMD="docker"
else
    echo "❌ 错误: 未找到 Docker 或 Podman"
    exit 1
fi

echo "🧪 测试 Garage S3 连接..."

# 检查服务是否运行
if ! $CONTAINER_CMD ps --format "{{.Names}}" | grep -q "^garage-s3$"; then
    echo "❌ Garage 容器未运行，请先运行 ./start-garage.sh"
    exit 1
fi

# 检查 S3 API 是否响应
echo "📡 检查 S3 API 连接..."
if curl -s http://127.0.0.1:3900 | grep -q "AccessDenied"; then
    echo "✅ S3 API 连接正常 (需要认证)"
else
    echo "❌ S3 API 连接失败"
    exit 1
fi

# 使用 AWS CLI 测试（如果安装了的话）
if command -v aws &> /dev/null; then
    echo "🔧 使用 AWS CLI 测试连接..."
    
    export AWS_ACCESS_KEY_ID="GK4d1095e87554826d40f6af89"
    export AWS_SECRET_ACCESS_KEY="02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
    export AWS_DEFAULT_REGION="garage"
    
    # 列出存储桶
    echo "📋 列出存储桶..."
    aws --endpoint-url=http://localhost:3900 s3 ls || echo "⚠️  存储桶列表可能为空"
    
    # 测试上传文件
    echo "📤 测试文件上传..."
    echo "Hello, Garage S3!" > /tmp/test-file.txt
    aws --endpoint-url=http://localhost:3900 s3 cp /tmp/test-file.txt s3://test-bucket/test-file.txt
    echo "✅ 文件上传成功"
    
    # 测试下载文件
    echo "📥 测试文件下载..."
    aws --endpoint-url=http://localhost:3900 s3 cp s3://test-bucket/test-file.txt /tmp/downloaded-file.txt
    if diff /tmp/test-file.txt /tmp/downloaded-file.txt > /dev/null; then
        echo "✅ 文件下载成功，内容一致"
    else
        echo "❌ 文件下载失败或内容不一致"
        exit 1
    fi
    
    # 测试删除文件
    echo "🗑️  测试文件删除..."
    aws --endpoint-url=http://localhost:3900 s3 rm s3://test-bucket/test-file.txt
    echo "✅ 文件删除成功"
    
    # 清理临时文件
    rm -f /tmp/test-file.txt /tmp/downloaded-file.txt
    
else
    echo "ℹ️  AWS CLI 未安装，跳过详细测试"
    echo "   可以安装 AWS CLI 进行更详细的测试："
    echo "   pip install awscli"
fi

echo ""
echo "🎉 S3 连接测试完成！"
echo ""
echo "📋 连接信息:"
echo "  - 端点: http://localhost:3900"
echo "  - 区域: garage"
echo "  - Access Key: GK4d1095e87554826d40f6af89"
echo "  - Secret Key: 02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
echo "  - 存储桶: test-bucket, test-multi-bucket"
echo ""
echo "🧪 现在可以运行 Go 测试了："
echo "  go test -v -run TestDelete.*S3 ./..."
