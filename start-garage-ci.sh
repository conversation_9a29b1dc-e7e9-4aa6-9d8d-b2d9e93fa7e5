#!/bin/bash

# Garage S3 启动脚本 - 专为 CI 环境优化
# 这个脚本专门为 GitHub Actions 等 CI 环境设计

set -e

echo "🚀 启动 Garage S3 服务 (CI 模式)..."

# 检查容器工具是否可用
CONTAINER_CMD="docker"
if command -v podman &> /dev/null && ! command -v docker &> /dev/null; then
    CONTAINER_CMD="podman"
elif ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker 或 Podman 未安装或不可用"
    exit 1
fi

echo "🔧 使用容器工具: $CONTAINER_CMD"

# 创建 Garage 配置文件
echo "📝 创建 Garage 配置文件..."
cat > garage.toml << 'EOF'
metadata_dir = "./garage-meta"
data_dir = "./garage-data"

db_engine = "sqlite"

replication_mode = "none"

rpc_bind_addr = "127.0.0.1:3901"
rpc_public_addr = "127.0.0.1:3901"
rpc_secret = "1799bccfd7411eddcf9ebd316bc1f5287ad12a68094e30c0de557895f2e6ca03"

[s3_api]
s3_region = "garage"
api_bind_addr = "127.0.0.1:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "127.0.0.1:3902"
root_domain = ".web.garage.localhost"

[admin]
api_bind_addr = "127.0.0.1:3903"
admin_token = "changeme"
metrics_token = "metricstoken"
EOF

# 启动 Garage 容器
echo "🐳 启动 Garage 容器..."
docker run -d --name garage-s3 \
  -p 3900-3904:3900-3904 \
  -v $(pwd)/garage.toml:/etc/garage.toml \
  -v $(pwd)/garage-meta:/garage-meta \
  -v $(pwd)/garage-data:/garage-data \
  docker.io/dxflrs/garage:v0.9.4 \
  /garage server

# 等待服务启动
echo "⏳ 等待 Garage S3 服务启动..."

# 等待容器启动
sleep 3

# 快速检测服务是否可用
for i in {1..30}; do
    # 检查容器是否在运行
    if ! docker ps -q -f name=garage-s3 | grep -q .; then
        echo "❌ 错误: Garage 容器未运行"
        docker logs garage-s3 2>/dev/null || true
        exit 1
    fi

    # 检查服务是否响应（更快的检测）
    if curl -s -m 2 --connect-timeout 1 http://127.0.0.1:3900 >/dev/null 2>&1; then
        echo "✅ Garage S3 服务已启动"
        break
    fi

    if [ $i -eq 30 ]; then
        echo "❌ 超时: Garage S3 服务启动失败"
        echo "=== 容器状态 ==="
        docker ps -f name=garage-s3
        echo "=== 容器日志 ==="
        docker logs garage-s3 2>/dev/null || true
        exit 1
    fi

    if [ $((i % 5)) -eq 0 ]; then
        echo "等待中... ($i/30)"
    fi
    sleep 1
done

# 配置集群布局
echo "🔧 配置集群布局..."
docker exec garage-s3 /garage layout assign -z dc1 -c 10G c0096371612fb6df
# 检查是否需要应用布局
if docker exec garage-s3 /garage layout show | grep -q "Current cluster layout version: 0"; then
    docker exec garage-s3 /garage layout apply --version 1
else
    echo "布局已经应用"
fi

# 创建新的访问密钥（使用时间戳确保唯一性）
TIMESTAMP=$(date +%s)
echo "🔑 创建访问密钥 (时间戳: $TIMESTAMP)..."
echo "创建 test-primary-$TIMESTAMP 密钥..."
PRIMARY_OUTPUT=$(docker exec garage-s3 /garage key create test-primary-$TIMESTAMP)
echo "$PRIMARY_OUTPUT"

echo "创建 test-secondary-$TIMESTAMP 密钥..."
SECONDARY_OUTPUT=$(docker exec garage-s3 /garage key create test-secondary-$TIMESTAMP)
echo "$SECONDARY_OUTPUT"

# 提取密钥信息
PRIMARY_KEY=$(echo "$PRIMARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
PRIMARY_SECRET=$(echo "$PRIMARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')
SECONDARY_KEY=$(echo "$SECONDARY_OUTPUT" | grep "Key ID:" | awk '{print $3}')
SECONDARY_SECRET=$(echo "$SECONDARY_OUTPUT" | grep "Secret key:" | awk '{print $3}')

echo "📝 提取的密钥信息:"
echo "Primary - Key: $PRIMARY_KEY"
echo "Primary - Secret: $PRIMARY_SECRET"
echo "Secondary - Key: $SECONDARY_KEY"
echo "Secondary - Secret: $SECONDARY_SECRET"

# 创建存储桶
echo "🪣 创建存储桶..."
docker exec garage-s3 /garage bucket create test-bucket 2>/dev/null || echo "存储桶 test-bucket 已存在"
docker exec garage-s3 /garage bucket create test-multi-bucket 2>/dev/null || echo "存储桶 test-multi-bucket 已存在"

# 设置存储桶权限
echo "🔐 设置存储桶权限..."
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-primary-$TIMESTAMP 2>/dev/null || true
docker exec garage-s3 /garage bucket allow test-bucket --read --write --key test-secondary-$TIMESTAMP 2>/dev/null || true
docker exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-primary-$TIMESTAMP 2>/dev/null || true
docker exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-secondary-$TIMESTAMP 2>/dev/null || true

# 更新配置文件中的密钥
echo "📝 更新 local.test.ini 配置文件..."
if [ -f "local.test.ini" ]; then
    # 备份原始配置文件
    cp local.test.ini local.test.ini.backup

    # 更新 primary 密钥
    sed -i "s/key = \"GK[^\"]*\"/key = \"$PRIMARY_KEY\"/g" local.test.ini
    sed -i "s/pass = \"[^\"]*\"/pass = \"$PRIMARY_SECRET\"/g" local.test.ini

    # 更新 secondary 密钥（需要更精确的替换）
    # 创建临时文件进行更精确的替换
    awk -v pk="$PRIMARY_KEY" -v ps="$PRIMARY_SECRET" -v sk="$SECONDARY_KEY" -v ss="$SECONDARY_SECRET" '
    /^\[\[connection_sources\.s3_providers\]\]/ { provider_count++ }
    /^key = / {
        if (provider_count == 1) print "key = \"" pk "\""
        else if (provider_count == 2) print "key = \"" sk "\""
        else print
        next
    }
    /^pass = / {
        if (provider_count == 1) print "pass = \"" ps "\""
        else if (provider_count == 2) print "pass = \"" ss "\""
        else print
        next
    }
    { print }
    ' local.test.ini > local.test.ini.tmp && mv local.test.ini.tmp local.test.ini

    echo "✅ 配置文件已更新"
    echo "=== 更新后的 S3 配置 ==="
    grep -A 10 "connection_sources.s3_providers" local.test.ini | head -20
else
    echo "❌ 警告: local.test.ini 文件不存在"
fi

# 验证配置
echo "✅ 验证配置..."
echo "=== 存储桶列表 ==="
docker exec garage-s3 /garage bucket list

echo "=== 访问密钥列表 ==="
docker exec garage-s3 /garage key list

# 测试 S3 API 连接
echo "🧪 测试 S3 API 连接..."
response=$(curl -s http://127.0.0.1:3900 2>/dev/null || echo "")
if echo "$response" | grep -q -E "(AccessDenied|InvalidRequest|SignatureDoesNotMatch)" || curl -s -f http://127.0.0.1:3900 >/dev/null 2>&1; then
    echo "✅ S3 API 连接正常"
    echo "API 响应: $response"
else
    echo "❌ S3 API 连接失败"
    echo "API 响应: $response"
    exit 1
fi

echo "🎉 Garage S3 服务配置完成！"
echo ""
echo "服务信息:"
echo "  S3 API: http://127.0.0.1:3900"
echo "  Admin API: http://127.0.0.1:3903"
echo "  Web UI: http://127.0.0.1:3902"
echo ""
echo "存储桶:"
echo "  - test-bucket"
echo "  - test-multi-bucket"
echo ""
echo "访问密钥:"
echo "  Primary: GK4d1095e87554826d40f6af89"
echo "  Secondary: GK1e8266fe625fd9e3d392d497"
