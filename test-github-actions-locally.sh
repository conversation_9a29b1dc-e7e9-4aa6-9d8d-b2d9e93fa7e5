#!/bin/bash

# 本地测试 GitHub Actions 工作流脚本
# 模拟 test-with-s3 job 的执行步骤

set -e

echo "🚀 开始本地测试 GitHub Actions 工作流..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_step() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 清理函数
cleanup() {
    log_step "清理环境"
    ./stop-garage-ci.sh 2>/dev/null || true
    docker rm -f mongo 2>/dev/null || true
    docker rm -f garage-s3 2>/dev/null || true
    log_success "清理完成"
}

# 设置陷阱以确保清理
trap cleanup EXIT

# 1. 检查前置条件
log_step "检查前置条件"

if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装"
    exit 1
fi

if ! command -v go &> /dev/null; then
    log_error "Go 未安装"
    exit 1
fi

if [ ! -f "local.test.ini" ]; then
    log_error "local.test.ini 文件不存在"
    exit 1
fi

log_success "前置条件检查通过"

# 2. 设置脚本权限
log_step "设置脚本权限"
chmod +x start-garage-ci.sh 2>/dev/null || log_warning "start-garage-ci.sh 不存在或已有权限"
chmod +x stop-garage-ci.sh 2>/dev/null || log_warning "stop-garage-ci.sh 不存在或已有权限"
log_success "脚本权限设置完成"

# 3. 启动 MongoDB
log_step "启动 MongoDB (Replica Set)"
docker run --rm -d --name mongo \
  -p 27017:27017 \
  mongo:latest \
  --replSet rs0 --bind_ip_all

# 4. 等待 MongoDB 启动
log_step "等待 MongoDB 启动"
for i in {1..20}; do
    if nc -z localhost 27017 2>/dev/null; then
        log_success "MongoDB 已启动"
        break
    fi
    if [ $i -eq 20 ]; then
        log_error "MongoDB 启动超时"
        exit 1
    fi
    echo "等待中... ($i/20)"
    sleep 1
done

# 5. 初始化 MongoDB 副本集
log_step "初始化 MongoDB 副本集"
docker run --rm --network host mongo:latest \
  mongosh --host localhost --eval 'rs.initiate({_id: "rs0", members: [{_id: 0, host: "localhost:27017"}]})'

# 6. 测试 MongoDB 连接
log_step "测试 MongoDB 连接"
docker run --rm --network host mongo:latest \
  mongosh --host localhost --eval 'db.testDocs.insertOne({ok: true})'
log_success "MongoDB 连接测试成功"

# 7. 启动 Garage S3 服务
log_step "启动 Garage S3 服务"
./start-garage-ci.sh
log_success "Garage S3 服务启动完成"

# 8. 验证 S3 服务状态
log_step "验证 S3 服务状态"
docker ps -f name=garage-s3
if curl -s http://127.0.0.1:3900 >/dev/null 2>&1; then
    log_success "S3 API 可访问"
else
    log_warning "S3 API 响应检查完成（可能需要认证）"
fi

# 9. 整理 Go 模块
log_step "整理 Go 模块"
go mod tidy
log_success "Go 模块整理完成"

# 10. 构建项目
log_step "构建项目"
go build -v ./...
log_success "项目构建成功"

# 11. 准备测试配置
log_step "准备测试配置"
# 备份原始配置
cp local.test.ini local.test.ini.backup

# 修改 MongoDB 连接字符串
sed -i.bak 's|mongodb://[^"]*|mongodb://localhost:27017/test|g' local.test.ini
echo "MongoDB 配置已更新："
grep "uri =" local.test.ini
log_success "测试配置准备完成"

# 12. 验证 S3 配置
log_step "验证 S3 配置"
echo "当前 S3 配置："
grep -A 10 "connection_sources.s3_providers" local.test.ini || log_warning "S3 配置检查完成"
echo "存储桶列表："
docker exec garage-s3 /garage bucket list || log_warning "存储桶列表检查完成"

# 13. 运行 S3 集成测试
log_step "运行 S3 集成测试"
if go test -v -run "TestDelete.*S3|TestWriteFileToS3_RealS3|TestDeleteS3Object_RealS3|TestCompleteChunkedUpload_MultiStorage" ./...; then
    log_success "S3 集成测试通过"
else
    log_error "S3 集成测试失败"
    echo "查看 Garage 容器日志："
    docker logs garage-s3 || true
    exit 1
fi

# 14. 运行完整测试套件
log_step "运行完整测试套件"
if go test -coverprofile=coverage.out ./...; then
    log_success "完整测试套件通过"
else
    log_error "完整测试套件失败"
    exit 1
fi

# 15. 检查覆盖率
log_step "检查覆盖率阈值 (>= 85%)"
if command -v bc &> /dev/null; then
    coverage=$(go tool cover -func=coverage.out | grep total: | awk '{print substr($3, 1, length($3)-1)}')
    echo "总覆盖率: $coverage%"
    threshold=85.0
    above=$(echo "$coverage >= $threshold" | bc -l)
    if [ "$above" -ne 1 ]; then
        log_error "代码覆盖率 ($coverage%) 低于阈值 ($threshold%)"
        exit 1
    else
        log_success "代码覆盖率 ($coverage%) 达到阈值 ($threshold%)"
    fi
else
    log_warning "bc 未安装，跳过覆盖率检查"
fi

# 16. 生成覆盖率报告
log_step "生成覆盖率报告"
echo "详细覆盖率报告："
go tool cover -func=coverage.out | grep -E "(Delete|delete|S3|s3)" || true
echo "整体覆盖率："
go tool cover -func=coverage.out | tail -1

# 17. 恢复配置文件
log_step "恢复配置文件"
mv local.test.ini.backup local.test.ini
log_success "配置文件已恢复"

log_success "🎉 本地 GitHub Actions 工作流测试完成！"
echo ""
echo "📊 测试结果总结："
echo "  ✅ MongoDB 副本集启动和连接"
echo "  ✅ Garage S3 服务启动和配置"
echo "  ✅ S3 集成测试通过"
echo "  ✅ 完整测试套件通过"
echo "  ✅ 代码覆盖率达标"
echo ""
echo "🚀 你的 GitHub Actions 工作流应该可以正常运行！"
