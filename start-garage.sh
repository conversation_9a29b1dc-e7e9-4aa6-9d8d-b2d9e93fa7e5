#!/bin/bash

# Garage S3 兼容服务启动脚本
# 用于支持 goupload 项目的 S3 测试

set -e

# 检查容器工具
CONTAINER_CMD=""
if command -v podman &> /dev/null; then
    CONTAINER_CMD="podman"
elif command -v docker &> /dev/null; then
    CONTAINER_CMD="docker"
else
    echo "❌ 错误: 未找到 Docker 或 Podman"
    echo ""
    echo "请安装其中一个容器工具："
    echo ""
    echo "安装 Docker:"
    echo "  curl -fsSL https://get.docker.com -o get-docker.sh"
    echo "  sudo sh get-docker.sh"
    echo "  sudo usermod -aG docker \$USER"
    echo "  # 重新登录或运行: newgrp docker"
    echo ""
    echo "或安装 Podman:"
    echo "  # Ubuntu/Debian:"
    echo "  sudo apt-get update"
    echo "  sudo apt-get install -y podman"
    echo ""
    echo "  # CentOS/RHEL/Fedora:"
    echo "  sudo dnf install -y podman"
    echo ""
    exit 1
fi

echo "🔧 使用容器工具: $CONTAINER_CMD"

GARAGE_VERSION="v0.9.4"
GARAGE_DATA_DIR="./garage-data"
GARAGE_META_DIR="./garage-meta"
GARAGE_CONFIG_FILE="./garage.toml"

echo "🚀 启动 Garage S3 兼容服务..."

# 创建数据目录
mkdir -p "$GARAGE_DATA_DIR"
mkdir -p "$GARAGE_META_DIR"

# 创建 Garage 配置文件
cat > "$GARAGE_CONFIG_FILE" << 'EOF'
metadata_dir = "/tmp/garage/meta"
data_dir = "/tmp/garage/data"

db_engine = "sqlite"

replication_mode = "none"

rpc_bind_addr = "127.0.0.1:3901"
rpc_public_addr = "127.0.0.1:3901"
rpc_secret = "1799bccfd7411eddcf9ebd316bc1f5287ad12a68094e30c0de557895f2e6ca63"

[s3_api]
s3_region = "garage"
api_bind_addr = "0.0.0.0:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "0.0.0.0:3902"
root_domain = ".web.garage.localhost"
index = "index.html"

[k2v_api]
api_bind_addr = "0.0.0.0:3904"

[admin]
api_bind_addr = "0.0.0.0:3903"
admin_token = "changeme"
metrics_token = "metricstoken"
EOF

echo "📝 Garage 配置文件已创建: $GARAGE_CONFIG_FILE"

# 检查是否已有 Garage 容器在运行
if $CONTAINER_CMD ps -a --format "{{.Names}}" | grep -q "^garage-s3$"; then
    echo "🔄 停止现有的 Garage 容器..."
    $CONTAINER_CMD stop garage-s3 || true
    $CONTAINER_CMD rm garage-s3 || true
fi

# 启动 Garage 容器
echo "🏃 启动 Garage 容器..."
$CONTAINER_CMD run -d \
    --name garage-s3 \
    --publish 3900:3900 \
    --publish 3901:3901 \
    --publish 3902:3902 \
    --publish 3903:3903 \
    --publish 3904:3904 \
    --volume "$(pwd)/$GARAGE_CONFIG_FILE:/etc/garage.toml:ro" \
    --volume "$(pwd)/$GARAGE_DATA_DIR:/tmp/garage/data" \
    --volume "$(pwd)/$GARAGE_META_DIR:/tmp/garage/meta" \
    docker.io/dxflrs/garage:$GARAGE_VERSION \
    /garage server

echo "⏳ 等待 Garage 服务启动..."
sleep 5

# 检查服务是否启动成功
if ! $CONTAINER_CMD ps --format "{{.Names}}" | grep -q "^garage-s3$"; then
    echo "❌ Garage 容器启动失败"
    $CONTAINER_CMD logs garage-s3
    exit 1
fi

echo "✅ Garage 服务已启动！"
echo ""
echo "📊 服务信息:"
echo "  - S3 API: http://localhost:3900"
echo "  - Admin API: http://localhost:3903"
echo "  - Web UI: http://localhost:3902"
echo "  - RPC: http://localhost:3901"
echo ""

# 等待服务完全启动
echo "⏳ 等待服务完全启动..."
for i in {1..30}; do
    if curl -s http://localhost:3900 > /dev/null 2>&1; then
        echo "✅ S3 API 服务已就绪！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 服务启动超时"
        exit 1
    fi
    sleep 1
done

echo ""
echo "🔧 配置 Garage 集群..."

# 配置集群节点
$CONTAINER_CMD exec garage-s3 /garage node id | head -1 > /tmp/garage_node_id.txt
NODE_ID=$(cat /tmp/garage_node_id.txt)
echo "节点 ID: $NODE_ID"

# 配置集群布局
$CONTAINER_CMD exec garage-s3 /garage layout assign $NODE_ID -z dc1 -c 1 || true
$CONTAINER_CMD exec garage-s3 /garage layout apply --version 1 || true

echo "⏳ 等待布局应用..."
sleep 3

echo ""
echo "👤 创建测试用户和密钥..."

# 创建测试用户 (对应配置文件中的 primary key)
$CONTAINER_CMD exec garage-s3 /garage key create test-primary || true
$CONTAINER_CMD exec garage-s3 /garage key import \
    --name test-primary \
    --access-key-id "GK4d1095e87554826d40f6af89" \
    --secret-access-key "02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3" || true

# 创建测试用户 (对应配置文件中的 secondary key)
$CONTAINER_CMD exec garage-s3 /garage key create test-secondary || true
$CONTAINER_CMD exec garage-s3 /garage key import \
    --name test-secondary \
    --access-key-id "GK1e8266fe625fd9e3d392d497" \
    --secret-access-key "0130431df8aa44ddee23777cfdc369b8dab1f51777d4308b49655aa0bdd85bc1" || true

echo ""
echo "🪣 创建测试存储桶..."

# 创建测试存储桶
$CONTAINER_CMD exec garage-s3 /garage bucket create test-bucket || true
$CONTAINER_CMD exec garage-s3 /garage bucket create test-multi-bucket || true

# 授权用户访问存储桶
$CONTAINER_CMD exec garage-s3 /garage bucket allow test-bucket --read --write --key test-primary || true
$CONTAINER_CMD exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-primary || true
$CONTAINER_CMD exec garage-s3 /garage bucket allow test-bucket --read --write --key test-secondary || true
$CONTAINER_CMD exec garage-s3 /garage bucket allow test-multi-bucket --read --write --key test-secondary || true

echo ""
echo "🎉 Garage S3 服务配置完成！"
echo ""
echo "📋 测试信息:"
echo "  - Primary Access Key: GK4d1095e87554826d40f6af89"
echo "  - Primary Secret Key: 02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
echo "  - Secondary Access Key: GK1e8266fe625fd9e3d392d497"
echo "  - Secondary Secret Key: 0130431df8aa44ddee23777cfdc369b8dab1f51777d4308b49655aa0bdd85bc1"
echo "  - 存储桶: test-bucket, test-multi-bucket"
echo "  - 区域: garage"
echo "  - 端点: http://localhost:3900"
echo ""
echo "🧪 现在可以运行 S3 相关的测试了："
echo "  go test -v -run TestDelete.*S3 ./..."
echo ""
echo "🛑 停止服务: ./stop-garage.sh"
