#!/bin/bash

# 在 Docker 容器中测试 GitHub Actions 工作流

set -e

echo "🐳 在 Docker 容器中测试 GitHub Actions 工作流..."

# 创建临时 Dockerfile
cat > Dockerfile.test << 'EOF'
FROM ubuntu:latest

# 安装基础工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    netcat-openbsd \
    bc \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# 安装 Go
RUN wget -O go.tar.gz https://go.dev/dl/go1.21.5.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go.tar.gz \
    && rm go.tar.gz

ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV PATH="${GOPATH}/bin:${PATH}"

# 设置工作目录
WORKDIR /workspace

# 复制项目文件
COPY . .

# 设置脚本权限
RUN chmod +x *.sh || true

CMD ["bash"]
EOF

echo "📦 构建测试 Docker 镜像..."
docker build -f Dockerfile.test -t goupload-test .

echo "🚀 在容器中运行测试..."
docker run --rm -it \
    --privileged \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v $(pwd):/workspace \
    -w /workspace \
    --network host \
    goupload-test \
    bash -c "
        echo '🔧 设置环境...'
        export PATH=/usr/local/go/bin:\$PATH
        go version
        
        echo '🧪 运行测试脚本...'
        ./test-github-actions-locally.sh
    "

# 清理
echo "🧹 清理临时文件..."
rm -f Dockerfile.test

echo "✅ Docker 测试完成！"
