package goupload

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	levelstore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// mockDeleteHelper 模拟deleteHelper接口
type mockDeleteHelper struct {
	mock.Mock
}

func (m *mockDeleteHelper) GetUserUploadConfig(site, entryName string) (*levelstore.UserUploadConfig, error) {
	args := m.Called(site, entryName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*levelstore.UserUploadConfig), args.Error(1)
}

func (m *mockDeleteHelper) GetS3ProviderMap(ctx context.Context, storage []levelstore.StorageConfig) (map[string]levelstore.S3ProviderConfig, error) {
	args := m.Called(ctx, storage)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]levelstore.S3ProviderConfig), args.Error(1)
}

func (m *mockDeleteHelper) GetS3Client(ctx context.Context, provider levelstore.S3ProviderConfig) (s3API, error) {
	args := m.Called(ctx, provider)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(s3API), args.Error(1)
}

func (m *mockDeleteHelper) DeleteS3Object(ctx context.Context, s3Client s3API, bucket, key string, opts WriteOptions) error {
	args := m.Called(ctx, s3Client, bucket, key, opts)
	return args.Error(0)
}

// 创建临时测试文件
func createTempTestFile(t *testing.T, content string) (string, func()) {
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "testfile.txt")

	err := os.WriteFile(testFile, []byte(content), 0644)
	require.NoError(t, err)

	return tempDir, func() {
		// TempDir会在测试结束后自动清理
	}
}

// 模拟Delete函数，用于测试
func mockDelete(ctx context.Context, helper *mockDeleteHelper, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error) {
	// 这个测试实现将使用mock替代实际的外部依赖
	config, err := helper.GetUserUploadConfig(site, entryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	s3ProviderMap, err := helper.GetS3ProviderMap(ctx, config.Storage)
	if err != nil {
		return nil, err
	}

	s3ClientMap := make(map[string]s3API)
	for name, p := range s3ProviderMap {
		client, err := helper.GetS3Client(ctx, p)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize s3 client for provider '%s': %w", name, err)
		}
		s3ClientMap[name] = client
	}

	writeOpts := extractWriteOptions()
	result := &DeleteResult{
		Path: relativePath,
	}

	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, storage := range config.Storage {
		wg.Add(1)
		go func(storage levelstore.StorageConfig) {
			defer wg.Done()

			switch storage.Type {
			case "local":
				fullPath := filepath.Join(storage.Path, relativePath)
				err := deleteLocalFile(fullPath)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type: "local",
						Path: fullPath,
					})
					mu.Unlock()
				} else if !os.IsNotExist(err) {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "local",
						Path:    fullPath,
						Message: err.Error(),
					})
					mu.Unlock()
				}

			case "s3":
				client, ok := s3ClientMap[storage.Target]
				if !ok {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: "s3 client not found",
					})
					mu.Unlock()
					return
				}

				provider := s3ProviderMap[storage.Target]
				err := helper.DeleteS3Object(ctx, client, storage.Bucket, relativePath, writeOpts)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type:     "s3",
						Path:     relativePath,
						Target:   storage.Target,
						Bucket:   storage.Bucket,
						Endpoint: provider.Endpoint,
					})
					mu.Unlock()
				} else {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: err.Error(),
					})
					mu.Unlock()
				}
			}
		}(storage)
	}

	wg.Wait()

	if len(result.FailedPaths) > 0 {
		result.IsPartialDelete = len(result.DeletedPaths) > 0
		if len(result.DeletedPaths) == 0 {
			return result, fmt.Errorf("all delete operations failed")
		}
	}

	if statsUpdater != nil && len(result.DeletedPaths) > 0 {
		parts := strings.Split(relativePath, "/")
		if len(parts) >= 2 {
			l1 := parts[0]
			l2 := parts[1]
			statsUpdater.AddDirStats(l1, l2, -1, -1)
		}
	}

	return result, nil
}

// 测试用例结构
type deleteTestCase struct {
	name           string
	setup          func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string
	validateResult func(t *testing.T, result *DeleteResult, err error)
	validateMocks  func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater)
	validateFiles  func(t *testing.T, path string)
}

func TestDelete(t *testing.T) {
	testCases := []deleteTestCase{
		{
			name: "成功删除本地文件",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				// 创建测试文件
				tempDir, cleanup := createTempTestFile(t, "测试内容")
				defer cleanup()
				relativePath := "test/path/testfile.txt"
				fullPath := filepath.Join(tempDir, relativePath)

				// 确保目录存在
				require.NoError(t, os.MkdirAll(filepath.Dir(fullPath), 0755))

				// 复制测试文件到测试路径
				require.NoError(t, os.WriteFile(fullPath, []byte("测试内容"), 0644))

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type: "local",
							Path: tempDir,
						},
					},
				}

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(nil, nil)
				stats.On("AddDirStats", "test", "path", -1, -1)

				return fullPath
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1)
				assert.Len(t, result.FailedPaths, 0)
				assert.False(t, result.IsPartialDelete)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				_, err := os.Stat(path)
				assert.True(t, os.IsNotExist(err), "文件应该已被删除")
			},
		},
		{
			name: "成功删除S3文件",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				relativePath := "test/path/testfile.txt"

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)
				mockS3.On("DeleteObject", mock.Anything, mock.Anything).Return(&s3.DeleteObjectOutput{}, nil)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(nil)
				stats.On("AddDirStats", "test", "path", -1, -1)

				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1)
				assert.Len(t, result.FailedPaths, 0)
				assert.False(t, result.IsPartialDelete)
				assert.Equal(t, "s3", result.DeletedPaths[0].Type)
				assert.Equal(t, "test-bucket", result.DeletedPaths[0].Bucket)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
		{
			name: "部分删除成功",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				// 创建测试文件
				tempDir, cleanup := createTempTestFile(t, "测试内容")
				defer cleanup()
				relativePath := "test/path/testfile.txt"
				fullPath := filepath.Join(tempDir, relativePath)

				// 确保目录存在
				require.NoError(t, os.MkdirAll(filepath.Dir(fullPath), 0755))

				// 复制测试文件到测试路径
				require.NoError(t, os.WriteFile(fullPath, []byte("测试内容"), 0644))

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type: "local",
							Path: tempDir,
						},
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(errors.New("模拟S3删除失败"))
				stats.On("AddDirStats", "test", "path", -1, -1)

				return fullPath
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err) // 部分成功仍然返回nil错误
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1) // 本地删除成功
				assert.Len(t, result.FailedPaths, 1)  // S3删除失败
				assert.True(t, result.IsPartialDelete)
				assert.Equal(t, "local", result.DeletedPaths[0].Type)
				assert.Equal(t, "s3", result.FailedPaths[0].Type)
				assert.Equal(t, "test-s3", result.FailedPaths[0].Target)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				_, err := os.Stat(path)
				assert.True(t, os.IsNotExist(err), "文件应该已被删除")
			},
		},
		{
			name: "完全删除失败",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				relativePath := "test/path/testfile.txt"

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(errors.New("模拟S3删除失败"))

				// 不应该调用StatsUpdater，因为没有成功的删除操作

				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.Error(t, err) // 完全失败返回错误
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 0) // 没有成功的删除
				assert.Len(t, result.FailedPaths, 1)  // S3删除失败
				assert.False(t, result.IsPartialDelete)

				// 验证错误消息
				assert.Contains(t, err.Error(), "all delete operations failed")
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertNotCalled(t, "AddDirStats", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
		{
			name: "配置获取错误",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(nil, errors.New("模拟配置获取失败"))
				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.Error(t, err)  // 应该返回错误
				assert.Nil(t, result) // 结果应为nil

				// 验证错误消息
				assert.Contains(t, err.Error(), "failed to get config")
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertNotCalled(t, "AddDirStats", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
	}

	// 为每个测试运行场景
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockHelper := new(mockDeleteHelper)
			mockStats := new(mockStatsUpdater)

			// 设置测试场景
			testPath := tc.setup(t, mockHelper, mockStats)

			// 执行模拟的删除操作
			relativePath := "test/path/testfile.txt"
			result, err := mockDelete(context.Background(), mockHelper, mockStats, "testsite", "testentry", relativePath)

			// 验证结果
			tc.validateResult(t, result, err)

			// 验证mock是否被正确调用
			tc.validateMocks(t, mockHelper, mockStats)

			// 验证文件状态
			if testPath != "" {
				tc.validateFiles(t, testPath)
			}
		})
	}
}

// TestDelete_Integration 测试删除功能的集成测试
func TestDelete_Integration(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 先上传一个文件
	content := "This is a test file for deletion."
	reader := strings.NewReader(content)

	uploadResult, err := Upload(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		"delete_test_user",
		reader,
		"delete_test.txt",
		0,
	)
	require.NoError(t, err, "上传应该成功")
	require.NotNil(t, uploadResult, "上传结果不应该为nil")

	// 验证文件确实被创建了
	assert.NotEmpty(t, uploadResult.WrittenPaths, "应该有写入路径")
	for _, writtenPath := range uploadResult.WrittenPaths {
		if writtenPath.Type == "local" {
			assert.FileExists(t, writtenPath.Path, "本地文件应该存在")
		}
	}

	// 现在删除文件
	deleteResult, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		uploadResult.Path,
	)

	// 验证删除结果
	assert.NoError(t, err, "删除应该成功")
	assert.NotNil(t, deleteResult, "删除结果不应该为nil")
	assert.Equal(t, uploadResult.Path, deleteResult.Path, "删除路径应该匹配")
	assert.NotEmpty(t, deleteResult.DeletedPaths, "应该有删除的路径")
	assert.Empty(t, deleteResult.FailedPaths, "不应该有失败的路径")
	assert.False(t, deleteResult.IsPartialDelete, "不应该是部分删除")

	// 验证文件确实被删除了
	for _, deletedPath := range deleteResult.DeletedPaths {
		if deletedPath.Type == "local" {
			assert.NoFileExists(t, deletedPath.Path, "本地文件应该被删除")
		}
	}
}

// TestDeleteLocalFile 测试本地文件删除
func TestDeleteLocalFile(t *testing.T) {
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "delete_test_*.txt")
	require.NoError(t, err, "创建临时文件应该成功")
	tempPath := tempFile.Name()
	_ = tempFile.Close()

	// 写入一些内容
	err = os.WriteFile(tempPath, []byte("test content"), 0644)
	require.NoError(t, err, "写入文件应该成功")

	// 验证文件存在
	assert.FileExists(t, tempPath, "文件应该存在")

	// 删除文件
	err = deleteLocalFile(tempPath)
	assert.NoError(t, err, "删除文件应该成功")

	// 验证文件被删除
	assert.NoFileExists(t, tempPath, "文件应该被删除")
}

// TestDeleteLocalFile_NonExistent 测试删除不存在的文件
func TestDeleteLocalFile_NonExistent(t *testing.T) {
	nonExistentPath := "/tmp/non_existent_file_12345.txt"

	// 确保文件不存在
	assert.NoFileExists(t, nonExistentPath, "文件不应该存在")

	// 删除不存在的文件应该成功（幂等性）
	err := deleteLocalFile(nonExistentPath)
	assert.NoError(t, err, "删除不存在的文件应该成功")
}

// TestDeleteLocalPath 测试删除本地路径（包括目录）
func TestDeleteLocalPath(t *testing.T) {
	// 创建临时目录和文件
	tempDir, err := os.MkdirTemp("", "delete_path_test_*")
	require.NoError(t, err, "创建临时目录应该成功")

	// 创建子目录和文件
	subDir := filepath.Join(tempDir, "subdir")
	err = os.MkdirAll(subDir, 0755)
	require.NoError(t, err, "创建子目录应该成功")

	testFile := filepath.Join(subDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err, "创建测试文件应该成功")

	// 验证目录和文件存在
	assert.DirExists(t, tempDir, "临时目录应该存在")
	assert.DirExists(t, subDir, "子目录应该存在")
	assert.FileExists(t, testFile, "测试文件应该存在")

	// 删除整个目录
	err = deleteLocalPath(tempDir)

	// 验证结果
	assert.NoError(t, err, "删除目录应该成功")

	// 验证目录被删除
	assert.NoDirExists(t, tempDir, "临时目录应该被删除")
}

// TestDelete_S3ClientInitializationError 测试S3客户端初始化失败
func TestDelete_S3ClientInitializationError(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 使用不存在的配置来测试配置获取失败
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"invalid_s3_config", // 不存在的配置
		"test/path.txt",
	)

	// 验证结果 - 应该返回配置相关错误
	assert.Error(t, err, "应该返回错误")
	assert.True(t,
		strings.Contains(err.Error(), "failed to get config") ||
			strings.Contains(err.Error(), "failed to initialize s3 client"),
		"错误信息应该包含配置或S3客户端相关错误，实际错误: %v", err)
	assert.Nil(t, result, "失败时应该返回nil")
}

// TestDelete_S3OnlySuccess 测试仅使用S3存储的成功删除
func TestDelete_S3OnlySuccess(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 首先上传一个文件到S3
	testFile := "test_s3_delete.txt"
	testContent := "This is a test file for S3 deletion"

	// 创建字符串读取器
	reader := strings.NewReader(testContent)

	// 上传文件
	uploadResult, err := Upload(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_s3_only",
		"test-user-123",
		reader,
		testFile,
		int64(len(testContent)),
	)
	if err != nil {
		t.Skipf("跳过测试：S3上传失败: %v", err)
	}

	// 删除文件
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_s3_only",
		uploadResult.Path,
	)

	// 验证结果
	assert.NoError(t, err, "删除应该成功")
	assert.NotNil(t, result, "应该返回删除结果")
	assert.Equal(t, uploadResult.Path, result.Path, "路径应该匹配")
	assert.Len(t, result.DeletedPaths, 1, "应该有一个删除的路径")
	assert.Equal(t, "s3", result.DeletedPaths[0].Type, "删除的类型应该是s3")
	assert.Equal(t, "test-garage-primary", result.DeletedPaths[0].Target, "目标应该匹配")
	assert.Equal(t, "test-bucket", result.DeletedPaths[0].Bucket, "存储桶应该匹配")
	assert.Empty(t, result.FailedPaths, "不应该有失败的路径")
	assert.False(t, result.IsPartialDelete, "不应该是部分删除")
}

// TestDelete_MultiStorageWithS3Success 测试混合存储（本地+S3）的成功删除
func TestDelete_MultiStorageWithS3Success(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 首先上传一个文件到混合存储（本地+S3）
	testFile := "test_multi_delete.txt"
	testContent := "This is a test file for multi-storage deletion"

	// 创建字符串读取器
	reader := strings.NewReader(testContent)

	// 上传文件
	uploadResult, err := Upload(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_multi_storage",
		"test-user-456",
		reader,
		testFile,
		int64(len(testContent)),
	)
	if err != nil {
		t.Skipf("跳过测试：混合存储上传失败: %v", err)
	}

	// 删除文件
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_multi_storage",
		uploadResult.Path,
	)

	// 验证结果
	assert.NoError(t, err, "删除应该成功")
	assert.NotNil(t, result, "应该返回删除结果")
	assert.Equal(t, uploadResult.Path, result.Path, "路径应该匹配")
	assert.Len(t, result.DeletedPaths, 2, "应该有两个删除的路径（本地+S3）")

	// 验证删除的路径类型
	deletedTypes := make(map[string]bool)
	for _, deletedPath := range result.DeletedPaths {
		deletedTypes[deletedPath.Type] = true
	}
	assert.True(t, deletedTypes["local"], "应该包含本地删除")
	assert.True(t, deletedTypes["s3"], "应该包含S3删除")

	assert.Empty(t, result.FailedPaths, "不应该有失败的路径")
	assert.False(t, result.IsPartialDelete, "不应该是部分删除")
}

// TestDelete_MixedStoragePartialFailure 测试混合存储的部分失败情况
func TestDelete_MixedStoragePartialFailure(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 创建一个临时文件用于测试
	tempDir, err := os.MkdirTemp("", "mixed_storage_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	testFile := filepath.Join(tempDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err)

	// 首先上传文件到混合存储
	reader := strings.NewReader("test content for mixed storage")
	uploadResult, err := Upload(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_multi_storage",
		"test-user-mixed",
		reader,
		"mixed_test.txt",
		int64(len("test content for mixed storage")),
	)
	if err != nil {
		t.Skipf("跳过测试：混合存储上传失败: %v", err)
	}

	// 现在删除本地文件，使本地删除失败，但S3删除成功
	// 通过修改本地存储路径中的文件权限来模拟部分失败
	localStoragePath := "test_uploads/multi"
	if _, err := os.Stat(localStoragePath); err == nil {
		// 如果本地存储路径存在，尝试修改权限
		err = os.Chmod(localStoragePath, 0000) // 移除所有权限
		if err == nil {
			defer os.Chmod(localStoragePath, 0755) // 恢复权限
		}
	}

	// 执行删除操作
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_multi_storage",
		uploadResult.Path,
	)

	// 验证结果 - 应该是部分成功
	if result != nil {
		// 如果有结果，验证是否为部分删除
		if len(result.DeletedPaths) > 0 && len(result.FailedPaths) > 0 {
			assert.True(t, result.IsPartialDelete, "应该是部分删除")
			t.Logf("部分删除成功：删除了 %d 个路径，失败了 %d 个路径",
				len(result.DeletedPaths), len(result.FailedPaths))
		} else {
			t.Logf("删除结果：删除了 %d 个路径，失败了 %d 个路径",
				len(result.DeletedPaths), len(result.FailedPaths))
		}
	} else {
		t.Logf("删除操作返回了错误: %v", err)
	}
}

// TestDelete_AllOperationsFailed 测试所有删除操作都失败的情况
func TestDelete_AllOperationsFailed(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 尝试删除一个不存在的文件，但在一个需要权限的路径
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		"/root/protected/nonexistent.txt", // 假设这个路径会导致权限错误
	)

	// 验证结果
	if err != nil {
		assert.Contains(t, err.Error(), "all delete operations failed", "应该返回所有操作失败的错误")
		assert.NotNil(t, result, "即使失败也应该返回结果")
		assert.Empty(t, result.DeletedPaths, "不应该有成功删除的路径")
		assert.NotEmpty(t, result.FailedPaths, "应该有失败的路径")
		assert.False(t, result.IsPartialDelete, "不应该是部分删除")
	} else {
		// 如果没有错误，说明文件不存在被认为是成功
		assert.NotNil(t, result, "结果不应该为nil")
	}
}

// TestDelete_StatsUpdateWithDifferentPaths 测试不同路径格式的统计更新
func TestDelete_StatsUpdateWithDifferentPaths(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	testCases := []struct {
		name         string
		relativePath string
		expectStats  bool
	}{
		{
			name:         "正常路径格式",
			relativePath: "level1/level2/file.txt",
			expectStats:  true,
		},
		{
			name:         "单级路径",
			relativePath: "file.txt",
			expectStats:  false, // 不足两级，不应该更新统计
		},
		{
			name:         "深层路径",
			relativePath: "level1/level2/level3/level4/file.txt",
			expectStats:  true, // 应该使用前两级
		},
		{
			name:         "空路径",
			relativePath: "",
			expectStats:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.relativePath == "" {
				// 空路径测试需要特殊处理
				return
			}

			// 由于我们使用真实的Delete函数，我们需要确保文件不存在
			// 这样删除操作会成功（因为不存在的文件被认为删除成功）
			result, err := Delete(
				context.Background(),
				statsUpdater,
				"TEST",
				"test_file",
				tc.relativePath,
			)

			// 验证结果
			assert.NoError(t, err, "删除操作应该成功")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, tc.relativePath, result.Path, "路径应该匹配")

			// 注意：由于文件不存在，DeletedPaths可能为空，但这仍然是成功的操作
		})
	}
}

// TestDeleteLocalPath_PermissionError 测试权限错误的情况
func TestDeleteLocalPath_PermissionError(t *testing.T) {
	if os.Getuid() == 0 {
		t.Skip("跳过权限测试：当前用户是root")
	}

	// 创建一个临时目录
	tempDir, err := os.MkdirTemp("", "permission_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建一个文件
	testFile := filepath.Join(tempDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test"), 0644)
	require.NoError(t, err)

	// 移除目录的写权限
	err = os.Chmod(tempDir, 0444) // 只读权限
	require.NoError(t, err)

	// 尝试删除文件应该失败
	err = deleteLocalPath(testFile)
	assert.Error(t, err, "删除应该失败由于权限不足")
	assert.True(t,
		strings.Contains(err.Error(), "failed to remove") ||
			strings.Contains(err.Error(), "failed to stat") ||
			strings.Contains(err.Error(), "permission denied"),
		"错误信息应该包含权限相关错误，实际错误: %v", err)

	// 恢复权限以便清理
	_ = os.Chmod(tempDir, 0755)
}

// TestDeleteLocalPath_DirectoryWithFiles 测试删除包含文件的目录
func TestDeleteLocalPath_DirectoryWithFiles(t *testing.T) {
	// 创建临时目录结构
	tempDir, err := os.MkdirTemp("", "delete_dir_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建嵌套目录和文件
	subDir1 := filepath.Join(tempDir, "subdir1")
	subDir2 := filepath.Join(tempDir, "subdir1", "subdir2")
	err = os.MkdirAll(subDir2, 0755)
	require.NoError(t, err)

	// 创建多个文件
	files := []string{
		filepath.Join(tempDir, "file1.txt"),
		filepath.Join(subDir1, "file2.txt"),
		filepath.Join(subDir2, "file3.txt"),
	}

	for _, file := range files {
		err = os.WriteFile(file, []byte("test content"), 0644)
		require.NoError(t, err)
		assert.FileExists(t, file, "文件应该存在")
	}

	// 验证目录结构存在
	assert.DirExists(t, tempDir, "根目录应该存在")
	assert.DirExists(t, subDir1, "子目录1应该存在")
	assert.DirExists(t, subDir2, "子目录2应该存在")

	// 删除整个目录
	err = deleteLocalPath(tempDir)
	assert.NoError(t, err, "删除目录应该成功")

	// 验证所有内容都被删除
	assert.NoDirExists(t, tempDir, "根目录应该被删除")
}

// TestDelete_EmptyStorageConfig 测试空存储配置
func TestDelete_EmptyStorageConfig(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "empty_storage", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 尝试删除文件，但配置中没有存储设置
	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"empty_storage", // 假设这个配置没有存储设置
		"test/path.txt",
	)

	// 验证结果
	if err != nil {
		// 如果配置不存在或有其他错误，这是预期的
		assert.Contains(t, err.Error(), "failed to get config", "应该是配置获取错误")
		assert.Nil(t, result, "错误时结果应该为nil")
	} else {
		// 如果成功，验证结果
		assert.NotNil(t, result, "结果不应该为nil")
		assert.Empty(t, result.DeletedPaths, "不应该有删除的路径")
		assert.Empty(t, result.FailedPaths, "不应该有失败的路径")
		assert.False(t, result.IsPartialDelete, "不应该是部分删除")
	}
}

// TestDelete_ConcurrentOperations 测试并发删除操作
func TestDelete_ConcurrentOperations(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 创建多个临时文件
	tempDir, err := os.MkdirTemp("", "concurrent_delete_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	numFiles := 5
	var wg sync.WaitGroup
	results := make([]*DeleteResult, numFiles)
	errors := make([]error, numFiles)

	// 并发执行多个删除操作
	for i := 0; i < numFiles; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			relativePath := fmt.Sprintf("concurrent/test_%d.txt", index)
			results[index], errors[index] = Delete(
				context.Background(),
				statsUpdater,
				"TEST",
				"test_file",
				relativePath,
			)
		}(i)
	}

	wg.Wait()

	// 验证所有操作都完成了
	for i := 0; i < numFiles; i++ {
		assert.NoError(t, errors[i], "删除操作 %d 应该成功", i)
		assert.NotNil(t, results[i], "结果 %d 不应该为nil", i)
	}
}

// TestDelete_LongPath 测试长路径处理
func TestDelete_LongPath(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 创建一个很长的路径
	longPath := strings.Repeat("very_long_directory_name/", 10) + "file.txt"

	result, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		longPath,
	)

	// 验证结果
	assert.NoError(t, err, "长路径删除应该成功")
	assert.NotNil(t, result, "结果不应该为nil")
	assert.Equal(t, longPath, result.Path, "路径应该匹配")
}

// TestDelete_SpecialCharactersInPath 测试路径中的特殊字符
func TestDelete_SpecialCharactersInPath(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	testCases := []struct {
		name string
		path string
	}{
		{
			name: "中文路径",
			path: "测试目录/测试文件.txt",
		},
		{
			name: "空格路径",
			path: "path with spaces/file with spaces.txt",
		},
		{
			name: "特殊字符",
			path: "special-chars_@#$/file.txt",
		},
		{
			name: "Unicode字符",
			path: "unicode/файл.txt",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := Delete(
				context.Background(),
				statsUpdater,
				"TEST",
				"test_file",
				tc.path,
			)

			// 验证结果
			assert.NoError(t, err, "特殊字符路径删除应该成功")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, tc.path, result.Path, "路径应该匹配")
		})
	}
}

// TestDeleteLocalPath_StatError 测试stat操作失败的情况
func TestDeleteLocalPath_StatError(t *testing.T) {
	// 尝试删除一个路径过长或无效的文件
	invalidPath := string(make([]byte, 300)) // 创建一个很长的无效路径
	for i := range invalidPath {
		invalidPath = invalidPath[:i] + "a" + invalidPath[i+1:]
	}
	invalidPath = "/invalid/" + invalidPath

	err := deleteLocalPath(invalidPath)

	// 应该成功，因为文件不存在
	assert.NoError(t, err, "删除不存在的文件应该成功")
}
