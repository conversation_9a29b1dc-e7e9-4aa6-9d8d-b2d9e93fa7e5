#!/bin/bash

# Garage S3 停止脚本 - 专为 CI 环境优化

set -e

echo "🛑 停止 Garage S3 服务 (CI 模式)..."

# 停止并删除容器
if docker ps -q -f name=garage-s3 | grep -q .; then
    echo "🐳 停止 Garage 容器..."
    docker rm -f garage-s3
    echo "✅ Garage 容器已停止"
else
    echo "ℹ️  Garage 容器未运行"
fi

# 清理文件（可选，CI 环境通常会自动清理）
if [ "${CI_CLEANUP:-false}" = "true" ]; then
    echo "🧹 清理临时文件..."
    rm -rf garage-data garage-meta garage.toml
    echo "✅ 临时文件已清理"
fi

echo "🎉 Garage S3 服务已停止"
